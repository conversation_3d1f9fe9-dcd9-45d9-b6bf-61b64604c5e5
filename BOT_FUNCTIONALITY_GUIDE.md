# 🤖 PocketBot - Browser Automation Guide

## Overview

PocketBot has been transformed into a powerful browser automation tool using Playwright. The app now features a beautiful bot interface that can control Chrome browsers and interact with web pages.

## 🚀 Getting Started

### 1. **Launch the App**
```bash
pnpm dev
```

### 2. **Bot Interface**
When you launch the app, you'll see a beautiful welcome screen with a **"Begin"** button.

## 🎯 Bot Features

### **Welcome Screen**
- Beautiful gradient background
- Large "🤖 PocketBot" title
- **"Begin" button** to start the bot

### **When You Click "Begin":**

1. **Window Resizes** - App window expands to 1200x800 for better bot control
2. **Chrome Browser Launches** - A visible Chrome browser window opens
3. **Navigates to Google** - Automatically goes to https://www.google.com
4. **DOM Analysis** - Scans and displays all interactive elements on the page

## 🎛️ Bot Control Panel

After clicking "Begin", you'll see a comprehensive control panel with three sections:

### **1. Configuration Panel**
- **Account Type**: Radio buttons for `Demo` or `Live`
- **Number**: Dropdown selector (1, 2, 3, 4, 5)
- **Search**: Text input field (default: "picture of a cat")

### **2. Controls Panel**
- **Start Bot** - Begins automated tasks
- **Pause/Resume** - Pauses or resumes bot execution
- **Stop** - Stops the bot completely

### **3. Browser Info Panel**
- **Current URL** - Shows the current page URL
- **DOM Elements Found** - Count of detected elements
- **Element List** - Shows detected buttons, inputs, links, etc.

## 🔧 Bot Functionality

### **Automatic Actions:**
1. **Browser Launch** - Opens Chrome with Playwright
2. **Google Navigation** - Goes to Google.com
3. **DOM Scanning** - Finds all interactive elements
4. **Search Execution** - Performs Google search based on configuration
5. **Real-time Updates** - Shows status and progress

### **Configuration Options:**

#### **Account Type**
- **Demo**: Safe testing mode
- **Live**: Production mode

#### **Number Selection**
- Choose from 1-5 for different bot behaviors

#### **Search Query**
- Custom search terms (default: "picture of a cat")
- Bot will search Google with this term

## 🎨 User Interface

### **Beautiful Design:**
- **Gradient Backgrounds** - Modern, professional look
- **Real-time Status** - Live updates with colored indicators
- **Responsive Layout** - Works on different screen sizes
- **Smooth Animations** - Polished user experience

### **Status Indicators:**
- 🟢 **Green** - Bot is running
- 🔴 **Red** - Bot is stopped
- **Status Text** - Detailed status messages

## 🔍 DOM Element Detection

The bot automatically detects and displays:
- **Buttons** - All clickable buttons
- **Input Fields** - Text inputs, search boxes
- **Links** - Navigation links
- **Headings** - Page structure elements
- **Interactive Elements** - Any clickable items

## 📋 Example Workflow

1. **Launch App** → See welcome screen
2. **Click "Begin"** → Window resizes, Chrome opens
3. **Configure Bot** → Set account type, number, search term
4. **Click "Start Bot"** → Bot begins automation
5. **Monitor Progress** → Watch real-time status updates
6. **View Results** → See DOM elements and page interactions

## 🛠️ Technical Features

### **Playwright Integration:**
- **Chrome Browser Control** - Full browser automation
- **DOM Querying** - Advanced element detection
- **Page Navigation** - Automatic URL handling
- **Search Automation** - Google search functionality

### **Real-time Communication:**
- **IPC Messaging** - Main ↔ Renderer communication
- **Status Updates** - Live bot status
- **DOM Updates** - Real-time element detection
- **Error Handling** - Graceful error management

## 🎯 Bot Capabilities

### **Current Features:**
✅ **Browser Launch** - Opens Chrome automatically  
✅ **Google Navigation** - Goes to Google.com  
✅ **DOM Scanning** - Detects page elements  
✅ **Search Execution** - Performs Google searches  
✅ **Real-time Status** - Live updates  
✅ **Window Resizing** - Adaptive UI  

### **Extensible Framework:**
The bot is built with an extensible architecture, making it easy to add:
- **Custom Websites** - Navigate to any URL
- **Form Filling** - Automatic form completion
- **Data Extraction** - Scrape page content
- **Multi-step Workflows** - Complex automation sequences
- **Custom Actions** - Site-specific behaviors

## 🚀 Running the Bot

### **Development Mode:**
```bash
pnpm dev
```
- Full bot functionality
- Chrome browser visible
- Real-time debugging
- Development tools available

### **Production Build:**
```bash
pnpm build:win  # Windows
pnpm build:mac  # macOS
pnpm build:linux # Linux
```

## 🔧 Customization

### **Adding New Websites:**
Modify `src/main/botService.ts` to add navigation to different sites:

```typescript
await this.navigateToUrl('https://example.com')
```

### **Custom Bot Actions:**
Extend the `executeBotTasks` method to add new automation:

```typescript
// Example: Fill a form
await this.page.fill('#username', 'myuser')
await this.page.click('#submit')
```

### **Configuration Options:**
Add new form fields in `BotInterface.tsx` for additional bot settings.

## 🎉 Summary

PocketBot is now a fully functional browser automation tool with:
- **Beautiful UI** with "Begin" button
- **Chrome browser control** via Playwright
- **Real-time DOM analysis** 
- **Configurable automation** settings
- **Professional interface** with status monitoring
- **Extensible architecture** for custom workflows

The bot provides a solid foundation for building complex web automation tasks while maintaining an intuitive and professional user interface.

**Ready to automate? Click "Begin" and watch PocketBot work! 🚀**
