import { ElectronAPI } from '@electron-toolkit/preload'

interface UpdateInfo {
  version: string
  releaseDate: string
  releaseName?: string
  releaseNotes?: string
}

interface UpdateProgress {
  bytesPerSecond: number
  percent: number
  transferred: number
  total: number
}

interface BotConfig {
  accountType: 'Demo' | 'Live'
  number: string
  search: string
}

interface BotStatus {
  isRunning: boolean
  isPaused: boolean
  currentUrl: string
  status: string
}

interface DomElement {
  tagName: string
  text?: string
  id?: string
  className?: string
  href?: string
  src?: string
}

interface UpdateAPI {
  checkForUpdates: () => Promise<{
    updateAvailable: boolean
    updateInfo?: UpdateInfo
    error?: string
    message?: string
  }>
  downloadUpdate: () => Promise<{
    success: boolean
    error?: string
    message?: string
  }>
  installUpdate: () => Promise<{
    success: boolean
    error?: string
    message?: string
  }>
  onUpdateChecking: (callback: () => void) => () => void
  onUpdateAvailable: (callback: (info: UpdateInfo) => void) => () => void
  onUpdateNotAvailable: (callback: (info: UpdateInfo) => void) => () => void
  onUpdateError: (callback: (error: string) => void) => () => void
  onUpdateDownloadProgress: (callback: (progress: UpdateProgress) => void) => () => void
  onUpdateDownloaded: (callback: (info: UpdateInfo) => void) => () => void

  // Bot APIs
  initializeBot?: () => Promise<{ success: boolean; error?: string }>
  navigateToUrl?: (url: string) => Promise<{ success: boolean; error?: string }>
  queryDomElements?: () => Promise<{ success: boolean; elements?: DomElement[]; error?: string }>
  startBot?: (config: BotConfig) => Promise<{ success: boolean; error?: string }>
  stopBot?: () => Promise<{ success: boolean; error?: string }>
  pauseBot?: (paused: boolean) => Promise<{ success: boolean; error?: string }>
  resizeWindow?: (width: number, height: number) => Promise<{ success: boolean; error?: string }>
  onBotStatusUpdate?: (callback: (status: BotStatus) => void) => () => void
  onDomElementsUpdate?: (callback: (elements: DomElement[]) => void) => () => void
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: UpdateAPI
  }
}
